<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">
              {{ isEditing ? `Edit Order #${order?.order_id}` : 'Create New Order' }}
            </h1>
            <p class="mt-1 text-sm text-gray-500">
              {{ isEditing ? 'Modify order details and items' : 'Add a new customer order to the system' }}
            </p>
          </div>
          <div class="flex items-center space-x-3">
            <Button @click="goBack" variant="ghost">
              Cancel
            </Button>
            <Button @click="saveOrder" :disabled="loading">
              {{ loading ? 'Saving...' : (isEditing ? 'Update Order' : 'Create Order') }}
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading && isEditing" class="flex justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error</h3>
          <div class="mt-2 text-sm text-red-700">{{ error }}</div>
        </div>
      </div>
    </div>

    <!-- Form -->
    <form @submit.prevent="saveOrder" class="space-y-6">
      <!-- Main Content Card -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Section - Input Fields and Table -->
            <div class="lg:col-span-2">
              <!-- Top Input Fields Side by Side -->
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                <div>
                  <h2 class="text-lg font-semibold text-gray-900 mb-2">Add Item:</h2>
                  <select
                    @change="addItemToOrder"
                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-lg py-3"
                  >
                    <option value="" selected disabled>Select new Item</option>
                    <option v-for="item in items" :key="item.id" :value="item.id">
                      {{ getName(item) }}
                    </option>
                  </select>
                </div>
                <div>
                  <h2 class="text-lg font-semibold text-gray-900 mb-2">Customer:</h2>
                  <select
                    v-model="form.user_id"
                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-lg py-3"
                    required
                  >
                    <option v-for="customer in customers" :key="customer.id" :value="customer.id">
                      {{ customer.name }}
                    </option>
                  </select>
                </div>
              </div>

              <!-- Table -->
              <div class="overflow-x-auto" style="min-height: 300px;">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Selling Price</th>
                      <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="(sale, index) in form.sales" :key="index">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-lg font-semibold text-gray-900">{{ getName(sale.item) }}</span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          v-model.number="sale.discount"
                          @change="calculate"
                          min="0"
                          step="0.01"
                          :max="sale.selling_price * sale.quantity"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-lg font-semibold"
                          required
                        />
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-lg font-semibold text-gray-900">{{ formatNumber(sale.selling_price) }}</span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right">
                        <span class="text-lg font-semibold text-gray-900">{{ formatNumber(sale.selling_price * sale.quantity) }}</span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-center">
                        <button
                          type="button"
                          @click="removeItem(index)"
                          class="inline-flex items-center px-3 py-1 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>


            <!-- Right Section - Calculation Summary -->
            <div class="lg:col-span-1">
              <div class="border rounded-lg p-4 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                <dl class="space-y-3">
                  <div class="flex justify-between">
                    <dt class="text-lg font-medium text-gray-700">Subtotal:</dt>
                    <dd class="text-lg font-semibold text-gray-900">{{ formatNumber(form.sub_total) }}</dd>
                  </div>

                  <div class="flex justify-between">
                    <dt class="text-lg font-medium text-gray-700">Total:</dt>
                    <dd class="text-lg font-semibold text-gray-900">{{ formatNumber(form.amount_total) }}</dd>
                  </div>

                  <div class="flex justify-between">
                    <dt class="text-lg font-medium text-gray-700">Discount:</dt>
                    <dd class="text-lg font-semibold text-gray-900">{{ formatNumber(form.discount) }}</dd>
                  </div>

                  <div class="space-y-2">
                    <dt class="text-lg font-medium text-gray-700">Amount paid:</dt>
                    <dd>
                      <input
                        type="number"
                        step="0.01"
                        v-model.number="form.amount_paid"
                        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-lg font-semibold text-red-600"
                        style="height: 50px; font-size: 1.1rem;"
                      />
                    </dd>
                  </div>
                </dl>

                <!-- Action Buttons -->
                <div class="mt-6 space-y-3">
                  <button
                    v-if="isReady"
                    type="submit"
                    :disabled="saving"
                    class="w-full flex justify-center items-center px-4 py-3 border border-transparent text-lg font-medium rounded-md text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50"
                  >
                    <svg v-if="saving" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"/>
                    </svg>
                    {{ saving ? 'Saving...' : 'Save and Exit' }}
                  </button>
                  <button
                    v-else
                    type="button"
                    @click="getReady"
                    class="w-full flex justify-center items-center px-4 py-3 border border-transparent text-lg font-medium rounded-md text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  >
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                    </svg>
                    Calculate
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Button } from '@/components/ui';
import { useApi } from '@/composables/useApi';
import axios from 'axios';

// Router
const route = useRoute();
const router = useRouter();
const orderId = computed(() => route.params.id as string);
const isEditing = computed(() => !!orderId.value);

// API setup
const { data: order, isLoading: loading, error: apiError, execute: fetchOrder } = useApi<any>(`/admin/orders/${orderId.value}`);
const error = computed(() => apiError.value?.message || null);

// Form data - matching the traditional order structure
const form = ref({
  user_id: '2', // Default to walk-in customer
  branch_id: '',
  description: '',
  discount: 0,
  vat: 0,
  sub_total: 0,
  amount_total: 0,
  amount_paid: 0,
  sales: [] as any[]
});

// State
const isReady = ref(false);
const customers = ref<any[]>([]);
const branches = ref<any[]>([]);
const items = ref<any[]>([]);
const saving = ref(false);

// Methods
const goBack = () => {
  router.push('/admin-spa/sales/orders');
};

// Load data from APIs
const loadCustomers = async () => {
  try {
    const response = await axios.get('/api/admin/users');
    customers.value = [
      { id: 2, name: 'Walk-in Customer', email: '<EMAIL>' },
      ...response.data.data
    ];
  } catch (err) {
    console.error('Error loading customers:', err);
    customers.value = [{ id: 2, name: 'Walk-in Customer', email: '<EMAIL>' }];
  }
};

const loadBranches = async () => {
  try {
    const response = await axios.get('/ajax-branches');
    branches.value = response.data;
  } catch (err) {
    console.error('Error loading branches:', err);
    branches.value = [];
  }
};

const loadItems = async () => {
  try {
    const response = await axios.get('/ajax-items');
    items.value = response.data;
  } catch (err) {
    console.error('Error loading items:', err);
    items.value = [];
  }
};

// Add item to order (matching traditional functionality)
const addItemToOrder = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  const itemId = target.value;

  if (!itemId) return;

  // Check if item already exists
  const existingItem = form.value.sales.find(sale => sale.item_id == itemId);
  if (existingItem) {
    target.value = '';
    return;
  }

  const item = items.value.find(i => i.id == itemId);
  if (item) {
    const sale = {
      item: item,
      item_id: item.id,
      quantity: 1,
      selling_price: item.target_amount || 0,
      discount: 0,
      vat: 0
    };
    form.value.sales.push(sale);
    target.value = '';
    calculate();
  }
};

const removeItem = (index: number) => {
  form.value.sales.splice(index, 1);
  calculate();
};

// Calculate totals (matching traditional logic)
const calculate = () => {
  isReady.value = false;
};

const getReady = () => {
  form.value.sub_total = 0;
  form.value.amount_total = 0;
  form.value.discount = 0;

  for (const sale of form.value.sales) {
    const amount = sale.quantity * sale.selling_price;
    form.value.discount += Number(sale.discount || 0);
    form.value.sub_total += amount;
  }

  form.value.amount_total = Number((form.value.sub_total - form.value.discount).toFixed(2));
  form.value.sub_total = Number(form.value.sub_total.toFixed(2));
  form.value.discount = Number(form.value.discount.toFixed(2));
  form.value.amount_paid = form.value.amount_total;
  isReady.value = true;
};

const saveOrder = async () => {
  if (!isReady.value) {
    alert('Please calculate the order totals first');
    return;
  }

  saving.value = true;
  try {
    const orderData = {
      order: {
        ...form.value,
        sales: form.value.sales
      }
    };

    if (isEditing.value) {
      await axios.put(`/api/admin/orders/${orderId.value}`, orderData);
    } else {
      await axios.post('/api/admin/orders', orderData);
    }

    router.push('/admin-spa/sales/orders');
  } catch (err) {
    console.error('Error saving order:', err);
    alert('Error saving order. Please try again.');
  } finally {
    saving.value = false;
  }
};

// Utility functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount || 0);
};

const formatNumber = (num: number) => {
  return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
};

const getName = (item: any) => {
  let name = item.name || '';
  if (item.reference_number) {
    name += ' ~ ' + item.reference_number;
  }
  return name;
};

// Watch for order data changes when editing
watch(order, (newOrder) => {
  if (newOrder && isEditing.value) {
    form.value = {
      user_id: newOrder.user_id || newOrder.customer_id,
      branch_id: newOrder.branch_id,
      description: newOrder.description,
      discount: newOrder.discount,
      vat: newOrder.vat,
      sub_total: newOrder.sub_total,
      amount_total: newOrder.amount_total,
      amount_paid: newOrder.amount_paid || 0,
      sales: newOrder.sales || []
    };
    getReady();
  }
}, { immediate: true });

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadCustomers(),
    loadBranches(),
    loadItems()
  ]);

  if (isEditing.value) {
    await fetchOrder();
  }
});
</script>
